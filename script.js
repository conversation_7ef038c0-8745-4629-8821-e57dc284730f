// Configuration
const GEMINI_API_KEY = 'YOUR_GEMINI_API_KEY'; // Replace with your actual API key
const myFullName = '<PERSON>';
const myDescription = `I am <PERSON>, a passionate full-stack developer with 5+ years of experience. I specialize in JavaScript, React, Node.js, Python, and UI/UX design. I love creating beautiful and functional web experiences that make a difference. I have worked on various projects including e-commerce platforms, task management apps, weather dashboards, and portfolio websites. I'm always eager to learn new technologies and take on challenging projects. I enjoy problem-solving and turning complex ideas into simple, elegant solutions.`;

// Rive Animation URLs (using public Rive community files)
const RIVE_ANIMATIONS = {
    hero: 'https://public.rive.app/community/runtime-files/2063-4080-avatar-pack-use-case.riv',
    profile: 'https://public.rive.app/community/runtime-files/1370-2487-magic-circle.riv',
    about: 'https://public.rive.app/community/runtime-files/1814-3665-coding-animation.riv',
    chat: 'https://public.rive.app/community/runtime-files/1370-2487-magic-circle.riv'
};

// Rive instances
let riveInstances = {};

// DOM Elements
const darkModeToggle = document.getElementById('darkModeToggle');
const chatBubble = document.getElementById('chatBubble');
const chatOverlay = document.getElementById('chatOverlay');
const chatClose = document.getElementById('chatClose');
const chatMessages = document.getElementById('chatMessages');
const chatInput = document.getElementById('chatInput');
const chatSend = document.getElementById('chatSend');

// Rive Initialization Functions
function initRiveAnimations() {
    // Initialize Hero Background Animation
    initHeroAnimation();

    // Initialize Profile Animation
    initProfileAnimation();

    // Initialize About Animation
    initAboutAnimation();

    // Initialize Chat Animation
    initChatAnimation();
}

function initHeroAnimation() {
    const canvas = document.getElementById('hero-rive-canvas');
    if (!canvas) return;

    try {
        const r = new rive.Rive({
            src: RIVE_ANIMATIONS.hero,
            canvas: canvas,
            autoplay: true,
            stateMachines: 'State Machine 1',
            fit: rive.Fit.Cover,
            alignment: rive.Alignment.Center,
            onLoad: () => {
                console.log('Hero animation loaded');
                r.resizeDrawingSurfaceToCanvas();
            },
            onLoadError: (err) => {
                console.log('Hero animation load error:', err);
                // Fallback: hide the animation container
                canvas.parentElement.style.display = 'none';
            }
        });

        riveInstances.hero = r;
    } catch (error) {
        console.log('Hero animation error:', error);
    }
}

function initProfileAnimation() {
    const canvas = document.getElementById('profile-rive-canvas');
    if (!canvas) return;

    try {
        const r = new rive.Rive({
            src: RIVE_ANIMATIONS.profile,
            canvas: canvas,
            autoplay: true,
            stateMachines: 'State Machine 1',
            fit: rive.Fit.Cover,
            alignment: rive.Alignment.Center,
            onLoad: () => {
                console.log('Profile animation loaded');
                r.resizeDrawingSurfaceToCanvas();
            },
            onLoadError: (err) => {
                console.log('Profile animation load error:', err);
                canvas.parentElement.style.display = 'none';
            }
        });

        riveInstances.profile = r;
    } catch (error) {
        console.log('Profile animation error:', error);
    }
}

function initAboutAnimation() {
    const canvas = document.getElementById('about-rive-canvas');
    if (!canvas) return;

    try {
        const r = new rive.Rive({
            src: RIVE_ANIMATIONS.about,
            canvas: canvas,
            autoplay: true,
            stateMachines: 'State Machine 1',
            fit: rive.Fit.Contain,
            alignment: rive.Alignment.Center,
            onLoad: () => {
                console.log('About animation loaded');
                r.resizeDrawingSurfaceToCanvas();
            },
            onLoadError: (err) => {
                console.log('About animation load error:', err);
                canvas.parentElement.style.display = 'none';
            }
        });

        riveInstances.about = r;
    } catch (error) {
        console.log('About animation error:', error);
    }
}

function initChatAnimation() {
    const canvas = document.getElementById('chat-rive-canvas');
    if (!canvas) return;

    try {
        const r = new rive.Rive({
            src: RIVE_ANIMATIONS.chat,
            canvas: canvas,
            autoplay: true,
            stateMachines: 'State Machine 1',
            fit: rive.Fit.Cover,
            alignment: rive.Alignment.Center,
            onLoad: () => {
                console.log('Chat animation loaded');
                r.resizeDrawingSurfaceToCanvas();
            },
            onLoadError: (err) => {
                console.log('Chat animation load error:', err);
                canvas.parentElement.style.display = 'none';
            }
        });

        riveInstances.chat = r;
    } catch (error) {
        console.log('Chat animation error:', error);
    }
}

// Rive Animation Utilities
function resizeRiveAnimations() {
    Object.values(riveInstances).forEach(instance => {
        if (instance && instance.resizeDrawingSurfaceToCanvas) {
            instance.resizeDrawingSurfaceToCanvas();
        }
    });
}

function pauseRiveAnimations() {
    Object.values(riveInstances).forEach(instance => {
        if (instance && instance.pause) {
            instance.pause();
        }
    });
}

function playRiveAnimations() {
    Object.values(riveInstances).forEach(instance => {
        if (instance && instance.play) {
            instance.play();
        }
    });
}

// Intersection Observer for performance optimization
function initAnimationObserver() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '50px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            const animationId = entry.target.id.replace('-rive-canvas', '');
            const instance = riveInstances[animationId];

            if (instance) {
                if (entry.isIntersecting) {
                    instance.play();
                } else {
                    instance.pause();
                }
            }
        });
    }, observerOptions);

    // Observe all Rive canvases
    ['hero-rive-canvas', 'profile-rive-canvas', 'about-rive-canvas', 'chat-rive-canvas'].forEach(id => {
        const canvas = document.getElementById(id);
        if (canvas) {
            observer.observe(canvas);
        }
    });
}

// Dark Mode Functionality
function initDarkMode() {
    const savedTheme = localStorage.getItem('theme') || 'light';
    document.documentElement.setAttribute('data-theme', savedTheme);
    updateDarkModeIcon(savedTheme);
}

function toggleDarkMode() {
    const currentTheme = document.documentElement.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    
    document.documentElement.setAttribute('data-theme', newTheme);
    localStorage.setItem('theme', newTheme);
    updateDarkModeIcon(newTheme);
}

function updateDarkModeIcon(theme) {
    const icon = darkModeToggle.querySelector('i');
    icon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
}

// Smooth Scrolling
function initSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Chat Functionality
let isFirstMessage = true;

function openChat() {
    chatOverlay.classList.add('active');
    document.body.style.overflow = 'hidden';
    
    if (isFirstMessage) {
        addBotMessage(`Hi! I am ${myFullName}, what do you want to know about me?`);
        isFirstMessage = false;
    }
    
    // Focus on input
    setTimeout(() => {
        chatInput.focus();
    }, 300);
}

function closeChat() {
    chatOverlay.classList.remove('active');
    document.body.style.overflow = 'auto';
}

function addMessage(content, isUser = false) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${isUser ? 'user' : 'bot'}`;
    
    const avatar = document.createElement('img');
    avatar.className = 'message-avatar';
    avatar.src = isUser 
        ? 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=50&h=50&fit=crop&crop=face'
        : 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=50&h=50&fit=crop&crop=face';
    avatar.alt = isUser ? 'User' : myFullName;
    
    const messageContent = document.createElement('div');
    messageContent.className = 'message-content';
    messageContent.textContent = content;
    
    messageDiv.appendChild(avatar);
    messageDiv.appendChild(messageContent);
    
    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function addBotMessage(content) {
    addMessage(content, false);
}

function addUserMessage(content) {
    addMessage(content, true);
}

function showTypingIndicator() {
    const typingDiv = document.createElement('div');
    typingDiv.className = 'message bot';
    typingDiv.id = 'typing-indicator';
    
    const avatar = document.createElement('img');
    avatar.className = 'message-avatar';
    avatar.src = 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=50&h=50&fit=crop&crop=face';
    avatar.alt = myFullName;
    
    const typingContent = document.createElement('div');
    typingContent.className = 'typing-indicator';
    typingContent.innerHTML = `
        <div class="typing-dots">
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
        </div>
    `;
    
    typingDiv.appendChild(avatar);
    typingDiv.appendChild(typingContent);
    
    chatMessages.appendChild(typingDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function hideTypingIndicator() {
    const typingIndicator = document.getElementById('typing-indicator');
    if (typingIndicator) {
        typingIndicator.remove();
    }
}

async function sendMessageToGemini(message) {
    const prompt = `User_message: ${message}. Reply naturally to the user message and if required then answer based on: ${myDescription} or just simply give friendly reply. And reply in a way that ${myFullName} is himself talking. Reply in short sentences.`;
    
    try {
        const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${GEMINI_API_KEY}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                contents: [{
                    parts: [{ text: prompt }]
                }]
            })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (data.candidates && data.candidates[0] && data.candidates[0].content) {
            return data.candidates[0].content.parts[0].text;
        } else {
            throw new Error('Invalid response format');
        }
    } catch (error) {
        console.error('Error calling Gemini API:', error);
        return "Sorry, I'm having trouble connecting right now. Please try again later!";
    }
}

async function handleSendMessage() {
    const message = chatInput.value.trim();
    if (!message) return;
    
    // Disable input and send button
    chatInput.disabled = true;
    chatSend.disabled = true;
    
    // Add user message
    addUserMessage(message);
    chatInput.value = '';
    
    // Show typing indicator
    showTypingIndicator();
    
    try {
        // Get response from Gemini
        const response = await sendMessageToGemini(message);
        
        // Hide typing indicator
        hideTypingIndicator();
        
        // Add bot response
        addBotMessage(response);
    } catch (error) {
        hideTypingIndicator();
        addBotMessage("Sorry, I'm having trouble responding right now. Please try again!");
    } finally {
        // Re-enable input and send button
        chatInput.disabled = false;
        chatSend.disabled = false;
        chatInput.focus();
    }
}

// Event Listeners
function initEventListeners() {
    // Dark mode toggle
    darkModeToggle.addEventListener('click', toggleDarkMode);
    
    // Chat functionality
    chatBubble.addEventListener('click', openChat);
    chatClose.addEventListener('click', closeChat);
    chatSend.addEventListener('click', handleSendMessage);
    
    // Chat input enter key
    chatInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSendMessage();
        }
    });
    
    // Close chat when clicking outside
    chatOverlay.addEventListener('click', (e) => {
        if (e.target === chatOverlay) {
            closeChat();
        }
    });
    
    // Prevent chat container clicks from closing overlay
    document.querySelector('.chat-container').addEventListener('click', (e) => {
        e.stopPropagation();
    });
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    initDarkMode();
    initSmoothScrolling();
    initEventListeners();

    // Initialize Rive animations after a short delay
    setTimeout(() => {
        initRiveAnimations();
        initAnimationObserver();
    }, 500);

    // Add some initial animation delays
    setTimeout(() => {
        document.body.style.opacity = '1';
    }, 100);
});

// Handle window resize for Rive animations
window.addEventListener('resize', () => {
    resizeRiveAnimations();
});

// Handle visibility change to pause/play animations
document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
        pauseRiveAnimations();
    } else {
        playRiveAnimations();
    }
});

// Add CSS for initial page load
document.body.style.opacity = '0';
document.body.style.transition = 'opacity 0.3s ease';
