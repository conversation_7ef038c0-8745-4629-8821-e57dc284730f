# Modern Portfolio Website with AI Chatbot

A responsive portfolio website with an integrated AI chatbot powered by Google's Gemini API.

## Features

- **Modern Design**: Clean, professional layout with smooth animations
- **Dark Mode**: Toggle between light and dark themes
- **Responsive**: Works perfectly on desktop, tablet, and mobile devices
- **AI Chatbot**: Interactive chatbot that can answer questions about you
- **Project Showcase**: Beautiful project cards with hover effects
- **Smooth Scrolling**: Seamless navigation between sections

## Setup Instructions

1. **Get Gemini API Key**:
   - Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Create a new API key
   - Copy the API key

2. **Configure the Website**:
   - Open `script.js`
   - Replace `'YOUR_GEMINI_API_KEY'` with your actual API key
   - Update `myFullName` with your name
   - Update `myDescription` with your personal description

3. **Customize Content**:
   - Update profile images URLs in `index.html`
   - Modify project information and images
   - Update contact information
   - Customize colors in CSS variables if desired

4. **Run the Website**:
   - Simply open `index.html` in your web browser
   - Or serve it using a local server for better performance

## File Structure

```
├── index.html          # Main HTML file
├── style.css           # Styles and responsive design
├── script.js           # JavaScript functionality
└── README.md           # This file
```

## Customization

### Colors
Edit the CSS variables in `style.css` to change the color scheme:

```css
:root {
    --primary-color: #6366f1;
    --secondary-color: #8b5cf6;
    --accent-color: #06b6d4;
    /* ... other variables */
}
```

### Content
- Update personal information in `index.html`
- Modify the `myDescription` variable in `script.js`
- Replace project images and descriptions
- Update social media links

### Chatbot Behavior
The chatbot uses your description to provide personalized responses. You can modify the prompt in the `sendMessageToGemini` function to change how the AI responds.

## Browser Compatibility

- Chrome (recommended)
- Firefox
- Safari
- Edge

## Notes

- Make sure to keep your API key secure
- The chatbot requires an internet connection to work
- Images are loaded from Unsplash for demo purposes - replace with your own images

## License

This project is open source and available under the MIT License.
