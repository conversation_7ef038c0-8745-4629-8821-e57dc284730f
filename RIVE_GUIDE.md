# 🎨 Rive Animation Integration Guide

This guide explains how to use Rive animations in your portfolio website and how to customize them.

## 🚀 What's Already Integrated

Your portfolio now includes Rive animations in these areas:

### 1. **Hero Section Background**
- **Location**: Behind the main hero content
- **Animation**: Avatar/character animation
- **Purpose**: Creates dynamic background movement

### 2. **Profile Picture Overlay**
- **Location**: Around your profile picture
- **Animation**: Magic circle/glow effect
- **Purpose**: Adds mystical/tech feel to your profile

### 3. **About Section**
- **Location**: Left side of the About section
- **Animation**: Coding/development animation
- **Purpose**: Illustrates your development skills

### 4. **Chat Bubble**
- **Location**: Floating chat button
- **Animation**: Subtle glow/pulse effect
- **Purpose**: Makes the chat more engaging

## 🛠️ How to Customize Rive Animations

### Option 1: Use Different Public Rive Files

1. **Browse Rive Community**: Visit [rive.app/community](https://rive.app/community)
2. **Find animations** you like
3. **Copy the runtime URL** (ends with `.riv`)
4. **Update the URLs** in `script.js`:

```javascript
const RIVE_ANIMATIONS = {
    hero: 'YOUR_NEW_HERO_ANIMATION_URL.riv',
    profile: 'YOUR_NEW_PROFILE_ANIMATION_URL.riv',
    about: 'YOUR_NEW_ABOUT_ANIMATION_URL.riv',
    chat: 'YOUR_NEW_CHAT_ANIMATION_URL.riv'
};
```

### Option 2: Create Your Own Rive Animations

1. **Download Rive Editor**: [rive.app](https://rive.app)
2. **Create your animation**
3. **Export as `.riv` file**
4. **Host the file** (GitHub, your server, etc.)
5. **Update the URL** in the configuration

### Option 3: Use Local Rive Files

1. **Place your `.riv` files** in your project folder
2. **Update the paths**:

```javascript
const RIVE_ANIMATIONS = {
    hero: './animations/hero.riv',
    profile: './animations/profile.riv',
    about: './animations/about.riv',
    chat: './animations/chat.riv'
};
```

## 🎯 Animation Controls

### State Machines
Many Rive animations have interactive state machines. You can trigger different states:

```javascript
// Example: Trigger a state in the hero animation
if (riveInstances.hero) {
    const stateMachine = riveInstances.hero.stateMachineInputs('State Machine 1');
    if (stateMachine) {
        const trigger = stateMachine.find(input => input.name === 'Trigger');
        if (trigger) {
            trigger.fire();
        }
    }
}
```

### Animation Events
You can listen to animation events:

```javascript
function initHeroAnimation() {
    const r = new rive.Rive({
        src: RIVE_ANIMATIONS.hero,
        canvas: document.getElementById('hero-rive-canvas'),
        autoplay: true,
        onLoad: () => {
            console.log('Animation loaded!');
        },
        onPlay: () => {
            console.log('Animation started playing');
        },
        onPause: () => {
            console.log('Animation paused');
        }
    });
}
```

## 🎨 Styling Rive Animations

### Adjust Opacity
```css
.hero-bg-animation {
    opacity: 0.3; /* Make background animation subtle */
}
```

### Change Size
```css
.about-animation {
    width: 100%;
    height: 500px; /* Adjust height */
}
```

### Add Filters
```css
.profile-animation canvas {
    filter: hue-rotate(45deg); /* Change colors */
}
```

## 📱 Performance Optimization

The integration includes several performance optimizations:

### 1. **Intersection Observer**
- Animations pause when not visible
- Saves CPU/battery on mobile

### 2. **Visibility API**
- Pauses all animations when tab is hidden
- Resumes when tab becomes active

### 3. **Error Handling**
- Graceful fallbacks if animations fail to load
- Console logging for debugging

## 🔧 Troubleshooting

### Animation Not Loading?
1. **Check the console** for error messages
2. **Verify the URL** is accessible
3. **Test with a different animation** from Rive community

### Performance Issues?
1. **Reduce animation complexity**
2. **Lower frame rate** in Rive editor
3. **Use smaller file sizes**

### Mobile Issues?
1. **Test on actual devices**
2. **Consider disabling** complex animations on mobile:

```javascript
if (window.innerWidth < 768) {
    // Skip heavy animations on mobile
    return;
}
```

## 🎭 Animation Ideas

### For Hero Section:
- Character animations
- Floating particles
- Geometric patterns
- Tech/coding themes

### For About Section:
- Skill visualizations
- Development tools
- Creative processes
- Data visualizations

### For Chat Bubble:
- Notification effects
- Pulse animations
- Message bubbles
- Communication icons

## 📚 Resources

- **Rive Website**: [rive.app](https://rive.app)
- **Rive Community**: [rive.app/community](https://rive.app/community)
- **Documentation**: [help.rive.app](https://help.rive.app)
- **Runtime Docs**: [rive.app/runtime](https://rive.app/runtime)

## 🚀 Next Steps

1. **Browse Rive Community** for animations you like
2. **Replace the default URLs** with your chosen animations
3. **Test on different devices** and screen sizes
4. **Customize the styling** to match your brand
5. **Consider creating** your own custom animations

Your portfolio now has dynamic, interactive animations that will make it stand out! 🎉
